# Semantic Agent

langchain_core 和 langchain_community 主要是 OpenAI 兼容模型的调用

pandas -> CSV, JSON 解析
   - dataframe -> 数据处理和转换

### 提示词调用 

- [compliance_review](../semantic_agent-master/plugins/customized_plugins/compliance_review)
- [security_qa](../semantic_agent-master/plugins/customized_plugins/security_qa)

### 直接模型调用 

- plugins/customized_plugins/yh0052000400003_internal/run.py
- plugins/customized_plugins/place_order/run.py
- plugins/customized_plugins/ct_counterparty_detail_internal/run.py
- plugins/customized_plugins/ct_counterparty_detail/run.py
- plugins/customized_plugins/YH0052000400003/run.py
- plugins/customized_plugins/YH0051030400004/run.py
- plugins/customized_plugins/CT_CounterpartyDetail/run.py

### 数据库查询

- 优化工具产出模型信息查询：[optimizer_statistics](../semantic_agent-master/plugins/customized_plugins/optimizer_statistics)
- 基金信息查询：[fund_infos_sql_query](../semantic_agent-master/plugins/customized_plugins/fund_infos_sql_query)
- 罚单数据库问答： [regulatory_db_qa](../semantic_agent-master/plugins/customized_plugins/regulatory_db_qa) 使用 Vanna 生成 MilvusVectorStore 的查询 SQL
https://github.com/vanna-ai/vanna

```mermaid
classDiagram
    class RegulatoryPunishmentDBQA {
        +post_llm(prompt: str) str
        +search_regulatory_db_info(query: str) str
    }
    class SQLPluginBase {
        +__init__()
        -_vanna_sql_base: VannaSQLBase
    }
    class VannaSQLBase {
        +__init__(base_dir)
        +vanna_config: VannaConfig
    }
    class MilvusVectorStore {
        +__init__(config: MilvusConfig, is_init)
    }
    class VannaBase
    class OpenAI_Chat

    RegulatoryPunishmentDBQA --|> SQLPluginBase
    SQLPluginBase o-- VannaSQLBase : composition
    VannaSQLBase --|> MilvusVectorStore
    VannaSQLBase --|> OpenAI_Chat
    MilvusVectorStore --|> VannaBase
```

```python
@sk_function(
    name="search_regulatory_db_info",
    description="""
    用于查询罚单和法规的基本信息，例如罚单发布时间、罚单发布机构、罚单文号、法规发文日期、法规发文机构、法规的生效日期等。
    """,
    input_description="用户的问题")
def search_regulatory_db_info(self, query: str) -> str:
    """
    检索模型信息
    """
```

### 库使用

- [punishment_analysis](../semantic_agent-master/plugins/customized_plugins/punishment_analysis) - milvus_client
- [derui_qa](../semantic_agent-master/plugins/customized_plugins/derui_qa) - RabbitMQ
- [cancel_order](../semantic_agent-master/plugins/customized_plugins/cancel_order) - Kafka
- [inquiry_order_price](../semantic_agent-master/plugins/customized_plugins/inquiry_order_price) -RedisClient

## 业务划分

[ct_counterparty_detail](../semantic_agent-master/plugins/customized_plugins/ct_counterparty_detail)
[ct_counterparty_detail_internal](../semantic_agent-master/plugins/customized_plugins/ct_counterparty_detail_internal)
[CT_CounterpartyDetail](../semantic_agent-master/plugins/customized_plugins/CT_CounterpartyDetail)
[ct_query_order_price](../semantic_agent-master/plugins/customized_plugins/ct_query_order_price)

### 订单管理

- [list_order](../semantic_agent-master/plugins/customized_plugins/list_order)
- [confirm_order](../semantic_agent-master/plugins/customized_plugins/confirm_order)
- [close_order](../semantic_agent-master/plugins/customized_plugins/close_order)
- [cancel_order](../semantic_agent-master/plugins/customized_plugins/cancel_order)
- [bargaining_order](../semantic_agent-master/plugins/customized_plugins/bargaining_order)
- [operate_order](../semantic_agent-master/plugins/customized_plugins/operate_order)
- [query_closeable_order](../semantic_agent-master/plugins/customized_plugins/query_closeable_order)
- [update_order](../semantic_agent-master/plugins/customized_plugins/update_order)

### QA

调用向量招回库：

- [regulatory_db_qa](../semantic_agent-master/plugins/customized_plugins/regulatory_db_qa)
- [regulatory_qa](../semantic_agent-master/plugins/customized_plugins/regulatory_qa)
- [security_qa](../semantic_agent-master/plugins/customized_plugins/security_qa)
- [tuoguan_qa](../semantic_agent-master/plugins/customized_plugins/tuoguan_qa)
- [derui_qa](../semantic_agent-master/plugins/customized_plugins/derui_qa)

```json
{
  "recall_url": "http://10.35.161.38:8805/retrieval/chunks/recall/top_n",
  "rank_url": "http://10.35.161.38:8805/retrieval/chunks/rank/top_n"
}
```