package com.phodal.semantic;

import org.springframework.stereotype.Service;

@Service
public class PlannerService {
    public String plan(String input) {
        ///  call DynamicModelService to load from config.json
//        String response = ChatClient.create(groqModel)
//                .prompt("Can you set an alarm 10 minutes from now?")
//                .tools(new DateTimeTools())
//                .call()
//                .content();
//
//        System.out.println(response);
//        return response;
        return "hello";
    }
}
