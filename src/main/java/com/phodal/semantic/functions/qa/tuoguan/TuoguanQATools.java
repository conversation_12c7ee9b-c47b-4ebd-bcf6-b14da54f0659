package com.phodal.semantic.functions.qa.tuoguan;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Component
public class TuoguanQATools {

    private static final String URL = "http://***********:32089/griffindoc-cgs/api/v1/doc/ask";
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Tool(description = "从托管业务知识库为用户的问题搜索答案")
    public String tuoguanSearch(String question) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("token", "griffinDocToken");
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));

        ObjectNode data = objectMapper.createObjectNode();
        data.put("question", question);
        data.put("accessControl", "tuoguan");

        HttpEntity<String> request = new HttpEntity<>(data.toString(), headers);

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(URL, request, String.class);
            if (response.getStatusCode() != HttpStatus.OK) {
                return "不知道";
            }
            JsonNode root = objectMapper.readTree(response.getBody());
            JsonNode list = root.path("data").path("retrieverSummarize").path("retrieverList").get(0).path("list");
            if (list.isArray() && list.size() > 0) {
                String content = list.get(0).path("content").asText();
                System.out.println(content);
                return content;
            } else {
                System.out.println("UNKNOW");
                return "不知道";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "不知道";
        }
    }
}